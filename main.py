import requests
import json

# ✅ Your API Key from Retell AI dashboard
API_KEY = "key_44cb83ddb132125e12debe9d2c6c"

# ✅ Base URL (may vary slightly depending on your Retell AI account region)
BASE_URL = "https://api.retellai.com/v1/calls"

# ✅ Set headers for authentication
headers = {"Authorization": f"Bearer {API_KEY}", "Content-Type": "application/json"}


# 🔄 Pagination function to fetch all calls
def get_all_calls():
    all_calls = []
    page = 1

    while True:
        params = {"page": page, "limit": 50}  # You can adjust this if needed

        response = requests.get(BASE_URL, headers=headers, params=params)

        if response.status_code != 200:
            print(f"❌ Error {response.status_code}: {response.text}")
            break

        data = response.json()
        calls = data.get("data", [])

        if not calls:
            print("✅ No more calls to fetch.")
            break

        all_calls.extend(calls)
        print(f"📦 Page {page} - {len(calls)} calls fetched.")
        page += 1

    return all_calls


# ✅ Fetch calls
calls_data = get_all_calls()

# 💾 Save to JSON file
with open("retellai_calls.json", "w", encoding="utf-8") as f:
    json.dump(calls_data, f, indent=4, ensure_ascii=False)

print(f"✅ Total {len(calls_data)} calls saved to retellai_calls.json")
